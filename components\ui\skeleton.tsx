import { cn } from '@/lib/utils';

function Skeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn('animate-pulse rounded-md bg-muted', className)}
      {...props}
    />
  );
}

// Specialized skeleton for markdown blocks
function MarkdownBlockSkeleton({
  type = 'mixed',
  className,
  lines = 3
}: {
  type?: 'paragraph' | 'heading' | 'code' | 'list' | 'mixed';
  className?: string;
  lines?: number;
}) {
  const containerClasses = cn('space-y-2 p-4', className);

  switch (type) {
    case 'paragraph':
      return (
        <div className={containerClasses}>
          {Array.from({ length: lines }).map((_, index) => (
            <Skeleton
              key={index}
              className={cn(
                'h-4',
                index === lines - 1 ? 'w-3/4' : 'w-full'
              )}
            />
          ))}
        </div>
      );

    case 'heading':
      return (
        <div className={containerClasses}>
          <Skeleton className="h-6 w-2/3 mb-3" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-4/5" />
        </div>
      );

    case 'code':
      return (
        <div className={containerClasses}>
          <Skeleton className="h-16 w-full rounded-lg" />
        </div>
      );

    case 'list':
      return (
        <div className={containerClasses}>
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="flex items-center space-x-2">
              <Skeleton className="h-2 w-2 rounded-full" />
              <Skeleton className="h-4 flex-1" />
            </div>
          ))}
        </div>
      );

    case 'mixed':
    default:
      return (
        <div className={containerClasses}>
          <Skeleton className="h-5 w-3/4 mb-2" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-5/6" />
          <Skeleton className="h-12 w-full mt-3 rounded-lg" />
        </div>
      );
  }
}

export { Skeleton, MarkdownBlockSkeleton };
