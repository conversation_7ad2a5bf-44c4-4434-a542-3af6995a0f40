'use client';

import React, { useEffect, useState, useRef } from 'react';
import { usePerformanceAlerts, PerformanceAlert } from '@/lib/performance-alerts';

interface PerformanceMetrics {
  fps: number;
  longTasks: number;
  memoryUsage: number;
  renderTime: number;
  workerTasks: number;
}

interface PerformanceMonitorProps {
  enabled?: boolean;
  onLongTask?: (duration: number) => void;
  longTaskThreshold?: number; // Default 50ms as recommended
}

export default function PerformanceMonitor({
  enabled = process.env.NODE_ENV === 'development',
  onLongTask,
  longTaskThreshold = 50
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 0,
    longTasks: 0,
    memoryUsage: 0,
    renderTime: 0,
    workerTasks: 0
  });

  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const frameCountRef = useRef(0);
  const lastTimeRef = useRef(performance.now());
  const longTaskCountRef = useRef(0);
  const workerTaskCountRef = useRef(0);
  const observerRef = useRef<PerformanceObserver | null>(null);

  const { startMonitoring, stopMonitoring, onAlert, getAlerts } = usePerformanceAlerts();

  useEffect(() => {
    if (!enabled) return;

    // Start performance alerting
    startMonitoring();

    // Setup alert listener
    const unsubscribe = onAlert((alert) => {
      setAlerts(prev => [...prev.slice(-9), alert]); // Keep last 10 alerts
      if (alert.type === 'long_task' && onLongTask) {
        onLongTask(alert.data?.duration || 0);
      }
    });

    // FPS monitoring
    const measureFPS = () => {
      frameCountRef.current++;
      const now = performance.now();
      const delta = now - lastTimeRef.current;

      if (delta >= 1000) {
        const fps = Math.round((frameCountRef.current * 1000) / delta);
        frameCountRef.current = 0;
        lastTimeRef.current = now;

        setMetrics(prev => ({ ...prev, fps }));
      }

      requestAnimationFrame(measureFPS);
    };

    requestAnimationFrame(measureFPS);

    // Long task monitoring
    if ('PerformanceObserver' in window) {
      try {
        observerRef.current = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          
          entries.forEach((entry) => {
            if (entry.entryType === 'longtask') {
              const duration = entry.duration;
              if (duration > longTaskThreshold) {
                longTaskCountRef.current++;
                onLongTask?.(duration);
                
                console.warn(`Long task detected: ${duration.toFixed(2)}ms`);
              }
            }
          });

          setMetrics(prev => ({ 
            ...prev, 
            longTasks: longTaskCountRef.current 
          }));
        });

        observerRef.current.observe({ entryTypes: ['longtask'] });
      } catch (error) {
        console.warn('PerformanceObserver not supported:', error);
      }
    }

    // Memory usage monitoring
    const updateMemoryUsage = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
        setMetrics(prev => ({ ...prev, memoryUsage: usedMB }));
      }
    };

    const memoryInterval = setInterval(updateMemoryUsage, 2000);

    // Worker task monitoring
    const originalPostMessage = Worker.prototype.postMessage;
    Worker.prototype.postMessage = function(...args) {
      workerTaskCountRef.current++;
      setMetrics(prev => ({ ...prev, workerTasks: workerTaskCountRef.current }));
      return originalPostMessage.apply(this, args);
    };

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
      clearInterval(memoryInterval);
      Worker.prototype.postMessage = originalPostMessage;
      stopMonitoring();
      unsubscribe();
    };
  }, [enabled, longTaskThreshold, onLongTask]);

  // Render time monitoring
  useEffect(() => {
    if (!enabled) return;

    const startTime = performance.now();
    
    return () => {
      const renderTime = performance.now() - startTime;
      setMetrics(prev => ({ ...prev, renderTime }));
    };
  });

  if (!enabled) return null;

  const getFPSColor = (fps: number) => {
    if (fps >= 55) return 'text-green-400';
    if (fps >= 30) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getLongTaskColor = (count: number) => {
    if (count === 0) return 'text-green-400';
    if (count < 5) return 'text-yellow-400';
    return 'text-red-400';
  };

  return (
    <div className="perf-monitor">
      <div className="space-y-1">
        <div className={`${getFPSColor(metrics.fps)}`}>
          FPS: {metrics.fps}
        </div>
        <div className={`${getLongTaskColor(metrics.longTasks)}`}>
          Long Tasks: {metrics.longTasks}
        </div>
        <div className="text-blue-400">
          Memory: {metrics.memoryUsage}MB
        </div>
        <div className="text-purple-400">
          Workers: {metrics.workerTasks}
        </div>
        <div className="text-gray-400">
          Render: {metrics.renderTime.toFixed(1)}ms
        </div>
        {alerts.length > 0 && (
          <div className="text-red-400 text-xs">
            Alerts: {alerts.length}
          </div>
        )}
      </div>
    </div>
  );
}

// Hook for performance monitoring
export function usePerformanceMonitor(enabled = false) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 0,
    longTasks: 0,
    memoryUsage: 0,
    renderTime: 0,
    workerTasks: 0
  });

  const longTaskCallbackRef = useRef<((duration: number) => void) | null>(null);

  const onLongTask = (callback: (duration: number) => void) => {
    longTaskCallbackRef.current = callback;
  };

  const clearLongTaskCallback = () => {
    longTaskCallbackRef.current = null;
  };

  return {
    metrics,
    onLongTask,
    clearLongTaskCallback,
    PerformanceMonitor: (props: Omit<PerformanceMonitorProps, 'onLongTask'>) => (
      <PerformanceMonitor 
        {...props} 
        enabled={enabled}
        onLongTask={longTaskCallbackRef.current || undefined}
      />
    )
  };
}

// Performance utilities
export const performanceUtils = {
  // Measure function execution time
  measure: <T extends (...args: any[]) => any>(
    fn: T,
    name?: string
  ): T => {
    return ((...args: any[]) => {
      const start = performance.now();
      const result = fn(...args);
      const end = performance.now();
      
      if (name) {
        console.log(`${name}: ${(end - start).toFixed(2)}ms`);
      }
      
      return result;
    }) as T;
  },

  // Debounce with performance tracking
  debounce: <T extends (...args: any[]) => any>(
    fn: T,
    delay: number,
    name?: string
  ): T => {
    let timeoutId: NodeJS.Timeout;
    
    return ((...args: any[]) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        const start = performance.now();
        fn(...args);
        const end = performance.now();
        
        if (name && end - start > 16) { // Log if > 1 frame
          console.log(`${name} (debounced): ${(end - start).toFixed(2)}ms`);
        }
      }, delay);
    }) as T;
  },

  // RAF-based throttle
  throttleRAF: <T extends (...args: any[]) => any>(fn: T): T => {
    let rafId: number | null = null;
    let lastArgs: any[] | null = null;

    return ((...args: any[]) => {
      lastArgs = args;
      
      if (rafId === null) {
        rafId = requestAnimationFrame(() => {
          if (lastArgs) {
            fn(...lastArgs);
          }
          rafId = null;
          lastArgs = null;
        });
      }
    }) as T;
  }
};
