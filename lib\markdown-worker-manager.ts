// Markdown Worker Manager
// Manages worker pool and provides high-level API for markdown processing

export interface MarkdownBlock {
  id: string;
  content: string;
  html?: string;
  startLine: number;
  endLine: number;
  cached?: boolean;
  error?: string;
}

export interface ParseResult {
  blocks: MarkdownBlock[];
  totalBlocks: number;
  cacheHits: number;
}

class MarkdownWorkerManager {
  private workers: Worker[] = [];
  private workerIndex = 0;
  private requestId = 0;
  private pendingRequests = new Map<number, {
    resolve: (value: any) => void;
    reject: (error: Error) => void;
    timeout: NodeJS.Timeout;
  }>();

  constructor() {
    this.initializeWorkers();
  }

  private initializeWorkers() {
    // Create worker pool (2x CPU cores as recommended)
    const workerCount = Math.max(2, (navigator.hardwareConcurrency || 4) * 2);
    
    for (let i = 0; i < workerCount; i++) {
      try {
        const worker = new Worker('/markdown-worker.js');
        worker.onmessage = this.handleWorkerMessage.bind(this);
        worker.onerror = this.handleWorkerError.bind(this);
        this.workers.push(worker);
      } catch (error) {
        console.error('Failed to create worker:', error);
      }
    }

    if (this.workers.length === 0) {
      console.warn('No workers created, falling back to main thread processing');
    }
  }

  private handleWorkerMessage(e: MessageEvent) {
    const { type, requestId, data, error } = e.data;
    const request = this.pendingRequests.get(requestId);
    
    if (!request) return;

    clearTimeout(request.timeout);
    this.pendingRequests.delete(requestId);

    if (type === 'ERROR') {
      request.reject(new Error(error));
    } else {
      request.resolve({ type, data });
    }
  }

  private handleWorkerError(error: ErrorEvent) {
    console.error('Worker error:', error);
  }

  private getNextWorker(): Worker | null {
    if (this.workers.length === 0) return null;
    
    const worker = this.workers[this.workerIndex];
    this.workerIndex = (this.workerIndex + 1) % this.workers.length;
    return worker;
  }

  private sendToWorker(type: string, data: any, timeoutMs = 10000): Promise<any> {
    return new Promise((resolve, reject) => {
      const worker = this.getNextWorker();
      
      if (!worker) {
        reject(new Error('No workers available'));
        return;
      }

      const requestId = ++this.requestId;
      
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(requestId);
        reject(new Error('Worker request timeout'));
      }, timeoutMs);

      this.pendingRequests.set(requestId, { resolve, reject, timeout });

      worker.postMessage({ type, data, requestId });
    });
  }

  // Parse full document into blocks
  async parseDocument(content: string): Promise<ParseResult> {
    try {
      const result = await this.sendToWorker('PARSE_FULL_DOCUMENT', { content });
      return result.data;
    } catch (error) {
      console.error('Worker parsing failed, falling back to main thread:', error);
      return this.fallbackParse(content);
    }
  }

  // Parse specific blocks
  async parseBlocks(blocks: MarkdownBlock[]): Promise<MarkdownBlock[]> {
    try {
      const result = await this.sendToWorker('PARSE_BLOCKS', { blocks });
      return result.data.blocks;
    } catch (error) {
      console.error('Worker block parsing failed:', error);
      return blocks; // Return original blocks on error
    }
  }

  // Clear worker caches
  async clearCache(): Promise<void> {
    const promises = this.workers.map(worker => 
      this.sendToWorker('CLEAR_CACHE', {}).catch(console.error)
    );
    await Promise.all(promises);
  }

  // Fallback parsing on main thread (simplified)
  private fallbackParse(content: string): ParseResult {
    const lines = content.split('\n');
    const blocks: MarkdownBlock[] = [];
    let blockId = 0;
    
    // Simple block splitting for fallback
    for (let i = 0; i < lines.length; i += 50) {
      const blockLines = lines.slice(i, i + 50);
      blocks.push({
        id: `fallback-block-${blockId++}`,
        content: blockLines.join('\n'),
        startLine: i,
        endLine: Math.min(i + 49, lines.length - 1),
        html: `<pre>${blockLines.join('\n')}</pre>` // Basic fallback
      });
    }

    return {
      blocks,
      totalBlocks: blocks.length,
      cacheHits: 0
    };
  }

  // Cleanup
  destroy() {
    this.workers.forEach(worker => worker.terminate());
    this.workers = [];
    
    // Clear pending requests
    this.pendingRequests.forEach(({ reject, timeout }) => {
      clearTimeout(timeout);
      reject(new Error('Worker manager destroyed'));
    });
    this.pendingRequests.clear();
  }
}

// Singleton instance
let workerManager: MarkdownWorkerManager | null = null;

export function getMarkdownWorkerManager(): MarkdownWorkerManager {
  if (!workerManager) {
    workerManager = new MarkdownWorkerManager();
  }
  return workerManager;
}

export function destroyMarkdownWorkerManager() {
  if (workerManager) {
    workerManager.destroy();
    workerManager = null;
  }
}
