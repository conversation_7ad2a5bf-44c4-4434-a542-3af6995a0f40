// Scroll synchronization for split mode
// Implements percentage-based sync with height correction as recommended

interface ScrollSyncOptions {
  throttleMs?: number;
  tolerance?: number;
  enableCorrection?: boolean;
}

interface ScrollElement {
  element: HTMLElement;
  id: string;
  lastScrollTop: number;
  lastScrollHeight: number;
  lastClientHeight: number;
}

class ScrollSynchronizer {
  private elements = new Map<string, ScrollElement>();
  private isUpdating = false;
  private throttleMs: number;
  private tolerance: number;
  private enableCorrection: boolean;
  private correctionTable = new Map<number, number>();
  private rafId: number | null = null;

  constructor(options: ScrollSyncOptions = {}) {
    this.throttleMs = options.throttleMs || 60; // 60ms as recommended
    this.tolerance = options.tolerance || 5;
    this.enableCorrection = options.enableCorrection !== false;
  }

  // Register an element for scroll synchronization
  register(element: HTMLElement, id: string) {
    const scrollElement: ScrollElement = {
      element,
      id,
      lastScrollTop: element.scrollTop,
      lastScrollHeight: element.scrollHeight,
      lastClientHeight: element.clientHeight
    };

    this.elements.set(id, scrollElement);

    // Add scroll listener with throttling
    const throttledHandler = this.throttle(
      (event: Event) => this.handleScroll(event, id),
      this.throttleMs
    );

    element.addEventListener('scroll', throttledHandler, { passive: true });

    // Store handler for cleanup
    (element as any).__scrollSyncHandler = throttledHandler;

    return () => this.unregister(id);
  }

  // Unregister an element
  unregister(id: string) {
    const scrollElement = this.elements.get(id);
    if (scrollElement) {
      const handler = (scrollElement.element as any).__scrollSyncHandler;
      if (handler) {
        scrollElement.element.removeEventListener('scroll', handler);
      }
      this.elements.delete(id);
    }
  }

  // Handle scroll event
  private handleScroll(event: Event, sourceId: string) {
    if (this.isUpdating) return;

    const sourceElement = this.elements.get(sourceId);
    if (!sourceElement) return;

    const { element } = sourceElement;
    const scrollTop = element.scrollTop;
    const scrollHeight = element.scrollHeight;
    const clientHeight = element.clientHeight;

    // Update source element data
    sourceElement.lastScrollTop = scrollTop;
    sourceElement.lastScrollHeight = scrollHeight;
    sourceElement.lastClientHeight = clientHeight;

    // Calculate scroll percentage
    const maxScroll = scrollHeight - clientHeight;
    const scrollPercentage = maxScroll > 0 ? scrollTop / maxScroll : 0;

    // Sync with other elements
    this.syncElements(sourceId, scrollPercentage);

    // Update correction table if enabled
    if (this.enableCorrection) {
      this.updateCorrectionTable(sourceId, scrollTop, scrollPercentage);
    }
  }

  // Synchronize scroll position with other elements
  private syncElements(sourceId: string, scrollPercentage: number) {
    this.isUpdating = true;

    // Use RAF for smooth updates
    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
    }

    this.rafId = requestAnimationFrame(() => {
      this.elements.forEach((targetElement, targetId) => {
        if (targetId === sourceId) return;

        const { element } = targetElement;
        const scrollHeight = element.scrollHeight;
        const clientHeight = element.clientHeight;
        const maxScroll = scrollHeight - clientHeight;

        if (maxScroll <= 0) return;

        // Calculate target scroll position
        let targetScrollTop = scrollPercentage * maxScroll;

        // Apply correction if enabled
        if (this.enableCorrection) {
          targetScrollTop = this.applyCorrectionTable(
            targetId, 
            targetScrollTop, 
            scrollPercentage
          );
        }

        // Only update if difference is significant
        const currentScrollTop = element.scrollTop;
        if (Math.abs(currentScrollTop - targetScrollTop) > this.tolerance) {
          element.scrollTop = targetScrollTop;
          
          // Update target element data
          targetElement.lastScrollTop = targetScrollTop;
          targetElement.lastScrollHeight = scrollHeight;
          targetElement.lastClientHeight = clientHeight;
        }
      });

      this.isUpdating = false;
      this.rafId = null;
    });
  }

  // Update correction table for better sync accuracy
  private updateCorrectionTable(sourceId: string, scrollTop: number, percentage: number) {
    // Store correction data for this percentage
    const key = Math.round(percentage * 100);
    this.correctionTable.set(key, scrollTop);

    // Limit table size to prevent memory issues
    if (this.correctionTable.size > 1000) {
      const keys = Array.from(this.correctionTable.keys()).sort((a, b) => a - b);
      const keysToDelete = keys.slice(0, 100); // Remove oldest 100 entries
      keysToDelete.forEach(key => this.correctionTable.delete(key));
    }
  }

  // Apply correction table for more accurate positioning
  private applyCorrectionTable(
    targetId: string, 
    calculatedPosition: number, 
    percentage: number
  ): number {
    const key = Math.round(percentage * 100);
    const correction = this.correctionTable.get(key);

    if (correction !== undefined) {
      // Apply a weighted correction based on how close we are to the stored value
      const weight = 0.3; // 30% correction weight
      return calculatedPosition * (1 - weight) + correction * weight;
    }

    return calculatedPosition;
  }

  // Throttle function for scroll events
  private throttle<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: NodeJS.Timeout | null = null;
    let lastExecTime = 0;

    return (...args: Parameters<T>) => {
      const currentTime = Date.now();

      if (currentTime - lastExecTime > delay) {
        func(...args);
        lastExecTime = currentTime;
      } else {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        timeoutId = setTimeout(() => {
          func(...args);
          lastExecTime = Date.now();
          timeoutId = null;
        }, delay - (currentTime - lastExecTime));
      }
    };
  }

  // Manually sync to a specific percentage
  syncToPercentage(percentage: number, excludeId?: string) {
    this.isUpdating = true;

    this.elements.forEach((element, id) => {
      if (id === excludeId) return;

      const { element: el } = element;
      const maxScroll = el.scrollHeight - el.clientHeight;
      
      if (maxScroll > 0) {
        const targetScrollTop = percentage * maxScroll;
        el.scrollTop = targetScrollTop;
        
        element.lastScrollTop = targetScrollTop;
      }
    });

    this.isUpdating = false;
  }

  // Get current sync state
  getSyncState() {
    const state: Record<string, any> = {};
    
    this.elements.forEach((element, id) => {
      const { element: el } = element;
      const maxScroll = el.scrollHeight - el.clientHeight;
      const percentage = maxScroll > 0 ? el.scrollTop / maxScroll : 0;
      
      state[id] = {
        scrollTop: el.scrollTop,
        scrollHeight: el.scrollHeight,
        clientHeight: el.clientHeight,
        percentage: percentage,
        maxScroll: maxScroll
      };
    });

    return state;
  }

  // Clear correction table
  clearCorrectionTable() {
    this.correctionTable.clear();
  }

  // Destroy synchronizer
  destroy() {
    // Unregister all elements
    const ids = Array.from(this.elements.keys());
    ids.forEach(id => this.unregister(id));

    // Cancel any pending RAF
    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
      this.rafId = null;
    }

    // Clear correction table
    this.clearCorrectionTable();
  }
}

// Singleton instance for global scroll sync
let globalScrollSync: ScrollSynchronizer | null = null;

export function getScrollSynchronizer(): ScrollSynchronizer {
  if (!globalScrollSync) {
    globalScrollSync = new ScrollSynchronizer();
  }
  return globalScrollSync;
}

export function destroyScrollSynchronizer() {
  if (globalScrollSync) {
    globalScrollSync.destroy();
    globalScrollSync = null;
  }
}

export { ScrollSynchronizer };
