// Markdown Worker for processing large documents
// This worker handles all markdown parsing to keep the main thread free

// Import markdown-it and plugins
importScripts('https://cdn.jsdelivr.net/npm/markdown-it@14.1.0/dist/markdown-it.min.js');
importScripts('https://cdn.jsdelivr.net/npm/markdown-it-highlightjs@4.1.0/dist/markdown-it-highlightjs.min.js');

// Initialize markdown-it instance
const md = markdownit({
  html: true,
  linkify: true,
  typographer: true,
  breaks: true
}).use(markdownitHighlightjs);

// Cache for parsed content
const parseCache = new Map();

// Generate cache key for content
function getCacheKey(content, options = {}) {
  return `${content.length}-${JSON.stringify(options)}-${content.slice(0, 100)}`;
}

// Split content into blocks for incremental processing
function splitIntoBlocks(content) {
  const blocks = [];
  const lines = content.split('\n');
  let currentBlock = [];
  let blockId = 0;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    currentBlock.push(line);
    
    // End block on empty line or specific patterns
    if (line.trim() === '' || 
        line.startsWith('#') || 
        line.startsWith('```') ||
        currentBlock.length >= 50) { // Max 50 lines per block
      
      if (currentBlock.length > 0) {
        blocks.push({
          id: `block-${blockId++}`,
          content: currentBlock.join('\n'),
          startLine: i - currentBlock.length + 1,
          endLine: i
        });
        currentBlock = [];
      }
    }
  }
  
  // Add remaining content as final block
  if (currentBlock.length > 0) {
    blocks.push({
      id: `block-${blockId++}`,
      content: currentBlock.join('\n'),
      startLine: lines.length - currentBlock.length,
      endLine: lines.length - 1
    });
  }
  
  return blocks;
}

// Process a single block
function processBlock(block) {
  const cacheKey = getCacheKey(block.content);
  
  if (parseCache.has(cacheKey)) {
    return {
      ...block,
      html: parseCache.get(cacheKey),
      cached: true
    };
  }
  
  try {
    const html = md.render(block.content);
    parseCache.set(cacheKey, html);
    
    return {
      ...block,
      html,
      cached: false
    };
  } catch (error) {
    console.error('Markdown parsing error:', error);
    return {
      ...block,
      html: `<div class="error">解析错误: ${error.message}</div>`,
      cached: false,
      error: error.message
    };
  }
}

// Message handler
self.onmessage = function(e) {
  const { type, data, requestId } = e.data;
  
  try {
    switch (type) {
      case 'PARSE_FULL_DOCUMENT':
        const blocks = splitIntoBlocks(data.content);
        const processedBlocks = blocks.map(processBlock);
        
        self.postMessage({
          type: 'PARSE_COMPLETE',
          requestId,
          data: {
            blocks: processedBlocks,
            totalBlocks: blocks.length,
            cacheHits: processedBlocks.filter(b => b.cached).length
          }
        });
        break;
        
      case 'PARSE_BLOCKS':
        const results = data.blocks.map(processBlock);
        
        self.postMessage({
          type: 'BLOCKS_PARSED',
          requestId,
          data: {
            blocks: results
          }
        });
        break;
        
      case 'CLEAR_CACHE':
        parseCache.clear();
        self.postMessage({
          type: 'CACHE_CLEARED',
          requestId
        });
        break;
        
      default:
        throw new Error(`Unknown message type: ${type}`);
    }
  } catch (error) {
    self.postMessage({
      type: 'ERROR',
      requestId,
      error: error.message
    });
  }
};

// Periodic cache cleanup (every 5 minutes)
setInterval(() => {
  if (parseCache.size > 1000) {
    parseCache.clear();
    self.postMessage({
      type: 'CACHE_CLEANED',
      data: { reason: 'size_limit' }
    });
  }
}, 5 * 60 * 1000);
