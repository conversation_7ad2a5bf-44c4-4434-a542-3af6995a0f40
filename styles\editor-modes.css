/* High-performance editor mode switching with CSS Grid */
/* Zero reflow mode switching as recommended in the performance guide */

.editor-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr;
  grid-template-areas: "source preview";
  gap: 0;
  contain: layout style paint; /* Performance optimization */
}

.editor-pane {
  overflow: hidden;
  contain: layout style paint;
  will-change: transform; /* Optimize for animations */
}

/* Source mode - only source editor visible */
.editor-grid.mode-source {
  grid-template-columns: 1fr;
  grid-template-areas: "source";
}

.mode-source .source-pane {
  grid-area: source;
  display: block;
}

.mode-source .preview-pane,
.mode-source .live-pane {
  display: none; /* Hidden, not rendered */
}

/* Preview mode - only preview visible */
.editor-grid.mode-preview {
  grid-template-columns: 1fr;
  grid-template-areas: "preview";
}

.mode-preview .preview-pane {
  grid-area: preview;
  display: block;
}

.mode-preview .source-pane,
.mode-preview .live-pane {
  display: none; /* Hidden, not rendered */
}

/* Split mode - both source and preview visible */
.editor-grid.mode-split {
  grid-template-columns: 1fr 1fr;
  grid-template-areas: "source preview";
}

.mode-split .source-pane {
  grid-area: source;
  display: block;
  border-right: 1px solid hsl(var(--border));
}

.mode-split .preview-pane {
  grid-area: preview;
  display: block;
}

.mode-split .live-pane {
  display: none; /* Hidden, not rendered */
}

/* Live mode - only live editor visible */
.editor-grid.mode-live {
  grid-template-columns: 1fr;
  grid-template-areas: "live";
}

.mode-live .live-pane {
  grid-area: live;
  display: block;
}

.mode-live .source-pane,
.mode-live .preview-pane {
  display: none; /* Hidden, not rendered */
}

/* Smooth transitions for mode switching */
.editor-grid {
  transition: grid-template-columns 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

.editor-pane {
  transition: opacity 100ms ease-in-out;
}

/* Performance optimizations */
.editor-pane {
  /* Use transform3d to enable hardware acceleration */
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  /* Optimize for animations and scrolling */
  will-change: transform, opacity;
}

/* Hidden panes optimization */
.editor-pane[style*="display: none"] {
  /* Use visibility instead of display for better performance in some cases */
  visibility: hidden;
  pointer-events: none;
  /* Prevent layout calculations */
  position: absolute;
  left: -9999px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .editor-grid.mode-split {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 1fr;
    grid-template-areas: 
      "source"
      "preview";
  }
  
  .mode-split .source-pane {
    border-right: none;
    border-bottom: 1px solid hsl(var(--border));
  }
}

/* Loading states and skeleton animations */
.editor-pane .skeleton {
  background: linear-gradient(90deg, 
    hsl(var(--muted)) 25%, 
    hsl(var(--muted-foreground) / 0.1) 50%, 
    hsl(var(--muted)) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Performance-optimized animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 200ms ease-out;
}

/* Scroll synchronization indicators */
.scroll-sync-indicator {
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background: hsl(var(--primary));
  border-radius: 2px;
  opacity: 0;
  transition: opacity 200ms ease-in-out;
  z-index: 10;
}

.scroll-sync-active .scroll-sync-indicator {
  opacity: 0.6;
}

/* Performance monitoring overlay (development only) */
.perf-monitor {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  z-index: 9999;
  pointer-events: none;
}

.perf-monitor.hidden {
  display: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .editor-pane {
    border: 2px solid;
  }
  
  .mode-split .source-pane {
    border-right-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .editor-grid,
  .editor-pane {
    transition: none;
  }
  
  .skeleton {
    animation: none;
  }
}
