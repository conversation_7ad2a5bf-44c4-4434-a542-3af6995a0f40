'use client';

import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { getMarkdownWorkerManager, MarkdownBlock } from '@/lib/markdown-worker-manager';
import { getDOMPatcher } from '@/lib/dom-patcher';
import { MarkdownBlockSkeleton } from '@/components/ui/skeleton';

interface VirtualizedRendererProps {
  content: string;
  className?: string;
  initialScrollPosition?: number;
  onScrollPositionChange?: (position: number) => void;
}

interface VirtualBlock extends MarkdownBlock {
  height?: number;
  element?: HTMLElement;
}

// Helper function to determine skeleton type based on content
function getSkeletonType(content: string): 'paragraph' | 'heading' | 'code' | 'list' | 'mixed' {
  if (content.includes('```')) return 'code';
  if (content.match(/^#+\s/m)) return 'heading';
  if (content.match(/^[\s]*[-*+]\s/m)) return 'list';
  if (content.split('\n').length <= 2) return 'paragraph';
  return 'mixed';
}

export default function VirtualizedRenderer({
  content,
  className = '',
  initialScrollPosition,
  onScrollPositionChange
}: VirtualizedRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [blocks, setBlocks] = useState<VirtualBlock[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const heightCache = useRef(new Map<string, number>());
  const workerManager = useMemo(() => getMarkdownWorkerManager(), []);
  const domPatcher = useMemo(() => getDOMPatcher(), []);
  const scrollRestoredRef = useRef(false);
  const parseTimeoutRef = useRef<NodeJS.Timeout>();

  // Estimate height for blocks
  const estimateHeight = useCallback((block: VirtualBlock): number => {
    if (heightCache.current.has(block.id)) {
      return heightCache.current.get(block.id)!;
    }
    
    // Estimate based on content
    const lines = block.content.split('\n').length;
    const hasCodeBlock = block.content.includes('```');
    const hasHeading = block.content.match(/^#+\s/m);
    
    let estimatedHeight = lines * 24; // Base line height
    
    if (hasCodeBlock) estimatedHeight += 40; // Extra for code blocks
    if (hasHeading) estimatedHeight += 20; // Extra for headings
    
    return Math.max(50, estimatedHeight); // Minimum height
  }, []);

  // Virtual scrolling setup with performance optimizations
  const virtualizer = useVirtualizer({
    count: blocks.length,
    getScrollElement: () => containerRef.current,
    estimateSize: (index) => estimateHeight(blocks[index]),
    overscan: 5, // Render 5 extra items above/below viewport for smoother scrolling
    measureElement: (element) => {
      // Use ResizeObserver for accurate height measurement
      const blockId = element.getAttribute('data-block-id');
      if (blockId && element.offsetHeight > 0) {
        heightCache.current.set(blockId, element.offsetHeight);
      }
      return element.offsetHeight;
    },
    // Enable smooth scrolling optimizations
    scrollPaddingStart: 0,
    scrollPaddingEnd: 0,
    // Use dynamic sizing for better performance
    lanes: 1,
  });

  // Throttled content parsing
  const parseContent = useCallback(async (newContent: string) => {
    if (parseTimeoutRef.current) {
      clearTimeout(parseTimeoutRef.current);
    }

    parseTimeoutRef.current = setTimeout(async () => {
      setIsLoading(true);
      try {
        const result = await workerManager.parseDocument(newContent);
        setBlocks(result.blocks);
      } catch (error) {
        console.error('Failed to parse content:', error);
      } finally {
        setIsLoading(false);
      }
    }, 200); // 200ms throttle as recommended
  }, [workerManager]);

  // Parse content when it changes
  useEffect(() => {
    parseContent(content);
    
    return () => {
      if (parseTimeoutRef.current) {
        clearTimeout(parseTimeoutRef.current);
      }
    };
  }, [content, parseContent]);

  // Handle scroll position changes
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      onScrollPositionChange?.(container.scrollTop);
    };

    // Throttle scroll events
    let scrollTimeout: NodeJS.Timeout;
    const throttledScroll = () => {
      if (scrollTimeout) clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(handleScroll, 16); // ~60fps
    };

    container.addEventListener('scroll', throttledScroll);
    return () => {
      container.removeEventListener('scroll', throttledScroll);
      if (scrollTimeout) clearTimeout(scrollTimeout);
    };
  }, [onScrollPositionChange]);

  // Restore initial scroll position
  useEffect(() => {
    if (containerRef.current && 
        initialScrollPosition !== undefined && 
        !scrollRestoredRef.current &&
        blocks.length > 0) {
      
      requestAnimationFrame(() => {
        if (containerRef.current) {
          containerRef.current.scrollTop = initialScrollPosition;
          scrollRestoredRef.current = true;
        }
      });
    }
  }, [initialScrollPosition, blocks.length]);

  // Update height cache when elements are measured
  const updateHeightCache = useCallback((blockId: string, height: number) => {
    heightCache.current.set(blockId, height);
    virtualizer.measure(); // Trigger remeasurement
  }, [virtualizer]);

  // Render virtual items
  const virtualItems = virtualizer.getVirtualItems();

  return (
    <div
      ref={containerRef}
      className={`overflow-auto h-full ${className}`}
      style={{
        contain: 'strict', // Performance optimization
      }}
    >
      {/* Loading indicator */}
      {isLoading && (
        <div className="absolute top-4 right-4 z-10">
          <div className="bg-background/80 backdrop-blur-sm rounded-lg px-3 py-2 text-sm">
            解析中...
          </div>
        </div>
      )}

      {/* Virtual container */}
      <div
        style={{
          height: virtualizer.getTotalSize(),
          width: '100%',
          position: 'relative',
        }}
      >
        {virtualItems.map((virtualItem) => {
          const block = blocks[virtualItem.index];
          if (!block) return null;

          return (
            <VirtualBlockRenderer
              key={block.id}
              block={block}
              virtualItem={virtualItem}
              onHeightMeasured={(height) => updateHeightCache(block.id, height)}
            />
          );
        })}
      </div>

      {/* Empty state */}
      {blocks.length === 0 && !isLoading && (
        <div className="flex items-center justify-center h-full text-muted-foreground">
          开始编写您的 Markdown 内容...
        </div>
      )}
    </div>
  );
}

// Individual block renderer with optimized DOM patching
interface VirtualBlockRendererProps {
  block: VirtualBlock;
  virtualItem: any;
  onHeightMeasured: (height: number) => void;
}

function VirtualBlockRenderer({
  block,
  virtualItem,
  onHeightMeasured
}: VirtualBlockRendererProps) {
  const elementRef = useRef<HTMLDivElement>(null);
  const previousHtmlRef = useRef<string>('');
  const domPatcher = useMemo(() => getDOMPatcher(), []);

  // Use optimized DOM patcher for efficient updates
  useEffect(() => {
    const element = elementRef.current;
    if (!element || !block.html) return;

    if (previousHtmlRef.current !== block.html) {
      // Queue patch operation with priority based on visibility
      const priority = virtualItem.index < 10 ? 'high' : 'normal';
      domPatcher.queuePatch(element, block.html, block.id, priority);
      previousHtmlRef.current = block.html;
    }

    // Measure height after content update
    const resizeObserver = new ResizeObserver((entries) => {
      const entry = entries[0];
      if (entry) {
        onHeightMeasured(entry.contentRect.height);
      }
    });

    resizeObserver.observe(element);

    // Listen for block resize events from DOM patcher
    const handleBlockResize = (event: CustomEvent) => {
      if (event.detail.blockId === block.id) {
        onHeightMeasured(element.offsetHeight);
      }
    };

    element.addEventListener('blockResize', handleBlockResize as EventListener);

    return () => {
      resizeObserver.disconnect();
      element.removeEventListener('blockResize', handleBlockResize as EventListener);
    };
  }, [block.html, block.id, virtualItem.index, onHeightMeasured, domPatcher]);

  return (
    <div
      ref={elementRef}
      className="prose prose-slate dark:prose-invert max-w-none p-4"
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        transform: `translateY(${virtualItem.start}px)`,
        minHeight: virtualItem.size,
      }}
      data-block-id={block.id}
    >
      {/* Enhanced skeleton loader while content is being parsed */}
      {!block.html && (
        <MarkdownBlockSkeleton
          type={getSkeletonType(block.content)}
          lines={Math.min(Math.max(block.content.split('\n').length, 2), 5)}
        />
      )}
    </div>
  );
}
