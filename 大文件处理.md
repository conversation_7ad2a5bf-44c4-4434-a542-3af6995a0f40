# 针对 8 000+ 行长文 & 四模式切换的性能改进方案  
> **目标**：任何场景下 ≥ 55 FPS、零闪白、切换延迟 < 150 ms  

---

## 一、整体思路：主线程做“排班”，Worker 做“体力活”

| 阶段 | 主线程职责 | Worker/后台职责 |
|------|------------|-----------------|
| **输入** | 监听 `docChanged` → 200 ms 节流 | — |
| **解析** | 把待渲染块 `postMessage` 出去 | `markdown-it` + KaTeX/Mermaid 解析 |
| **渲染补丁** | 在 `requestAnimationFrame` 批量 `morphdom` → DOM Patch | — |
| **滚动** | 仅计算可见块 + Overscan；位移 `transform` | — |
| **模式切换** | 改 CSS 布局 / hidden 属性；打开缓存 | — |

> **关键**：主线程永远不做重计算；一次只补丁“进入视窗的块”。

---

## 二、核心技术与库

| 任务                     | 选型 / 自研                     | 关键配置 |
|--------------------------|----------------------------------|----------|
| 块级虚拟滚动             | `@tanstack/virtual` + 高度缓存   | overscan = 1×viewport |
| Markdown 解析            | WebWorker + `markdown-it`        | Worker pool (2×CPU) |
| HTML Patch               | `morphdom`（或 `nano-morph`）    | 批量 patch in RAF |
| 滚动同步 (双栏)          | 百分比同步 + 高度差修正表        | 节流 60 ms |
| 图片/公式懒加载         | `IntersectionObserver`           | rootMargin 1×viewport |
| 模式切换布局            | CSS Grid + `display:none` 隐藏   | 样式类 `.mode-*` |
| 全局缓存                 | `WeakMap<BlockID, {html,height}>`| 首次渲染后即写 |
| 安装提示 / PWA          | `next-pwa@6` + `beforeinstallprompt` | `display:standalone` |
| 监控                    | Chrome Perf + Lighthouse CI + `@sentry/browser` | LongTask 报警阈值 50 ms |

---

## 三、关键改进点逐条对照

| 旧瓶颈 | 改进思路 | 核心代码/配置 |
|--------|----------|---------------|
| *整页 `innerHTML` 触发 Reflow* | **DOM diff patch** 只改变区块 | `morphdom(oldNode, newHTML)` |
| *虚拟列表估高误差导致闪白* | **高度缓存 + skeleton 灰占位** | `ResizeObserver` 写 `heightMap` |
| *模式切换重跑解析* | **数据层共享、视图壳复用** | `previewPane.hidden = !isPreview` |
| *滚动同步行号遍历* | **百分比同步** + 校正表 | `preview.scrollTop = perc * (H - h)` |
| *KaTeX/Mermaid 阻塞* | **Worker 渲染 + 懒执行** | `importScripts('katex.min.js')` |
| *功能页加载拖慢编辑区* | **RSC / lazy + Suspense** | `const Market = lazy(()=>import(...))` |

---

## 四、性能目标

| 场景                    | 指标                         |
|-------------------------|------------------------------|
| 文档滚动（8000 行）     | ≥ 55 FPS，Main 帧 ≤ 5 ms      |
| 模式切换（四态互换）    | UI 可交互延迟 ≤ 150 ms        |
| Worker 首屏解析         | ≤ 800 ms（后台完成）          |
| DOM Patch 单帧          | ≤ 6 ms（批量 30 块）          |
| 安装 PWA 后离线启动     | 断网可写、FCP ≤ 1.2 s         |

---

## 五、落地顺序

1. **引入 Worker 渲染** — 完全搬出解析负荷  
2. **实现块级高度缓存 + 虚拟列表** — 去掉闪白  
3. **接入 `morphdom` 批量 patch** — 杜绝整页更新  
4. **CSS 模式切换壳** — hidden / grid-area，0 重排  
5. **Overscan & skeleton 微调** — 体验抛光  
6. **监控 & 长任务报警** — 防回退

> 按此路线即可确保在 8000+ 行长文下，无论滚动、拖拽还是切模式，都流畅且无闪屏。
