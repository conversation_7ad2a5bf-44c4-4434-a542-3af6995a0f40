// High-performance DOM patcher using morphdom
// Implements batched RAF-based updates as recommended in the performance guide

import morphdom from 'morphdom';

interface PatchOperation {
  element: HTMLElement;
  newHTML: string;
  priority: 'high' | 'normal' | 'low';
  blockId: string;
}

interface PatchBatch {
  operations: PatchOperation[];
  timestamp: number;
}

class DOMPatcher {
  private pendingPatches = new Map<string, PatchOperation>();
  private rafId: number | null = null;
  private isPatching = false;
  private patchQueue: PatchBatch[] = [];
  private maxBatchSize = 30; // Max 30 blocks per frame as recommended
  private frameTimeLimit = 6; // Max 6ms per frame as recommended

  constructor() {
    this.bindMethods();
  }

  private bindMethods() {
    this.processPatchBatch = this.processPatchBatch.bind(this);
  }

  // Queue a patch operation
  queuePatch(
    element: HTMLElement, 
    newHTML: string, 
    blockId: string, 
    priority: 'high' | 'normal' | 'low' = 'normal'
  ) {
    // Deduplicate patches for the same block
    this.pendingPatches.set(blockId, {
      element,
      newHTML,
      priority,
      blockId
    });

    this.schedulePatchBatch();
  }

  // Schedule batch processing in next RAF
  private schedulePatchBatch() {
    if (this.rafId !== null) return;

    this.rafId = requestAnimationFrame(() => {
      this.rafId = null;
      this.processPatchBatch();
    });
  }

  // Process patches in batches with time slicing
  private processPatchBatch() {
    if (this.isPatching || this.pendingPatches.size === 0) return;

    this.isPatching = true;
    const startTime = performance.now();

    // Convert pending patches to array and sort by priority
    const operations = Array.from(this.pendingPatches.values()).sort((a, b) => {
      const priorityOrder = { high: 0, normal: 1, low: 2 };
      return priorityOrder[a.priority] - priorityOrder[b.priority];
    });

    // Clear pending patches
    this.pendingPatches.clear();

    // Process operations in chunks
    let processedCount = 0;
    const maxOperations = Math.min(operations.length, this.maxBatchSize);

    for (let i = 0; i < maxOperations; i++) {
      const operation = operations[i];
      
      try {
        this.applyPatch(operation);
        processedCount++;

        // Check if we're approaching frame time limit
        const elapsed = performance.now() - startTime;
        if (elapsed > this.frameTimeLimit && i < maxOperations - 1) {
          // Queue remaining operations for next frame
          const remaining = operations.slice(i + 1);
          remaining.forEach(op => {
            this.pendingPatches.set(op.blockId, op);
          });
          break;
        }
      } catch (error) {
        console.error(`Failed to patch block ${operation.blockId}:`, error);
      }
    }

    this.isPatching = false;

    // Schedule next batch if there are remaining patches
    if (this.pendingPatches.size > 0) {
      this.schedulePatchBatch();
    }

    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development') {
      const elapsed = performance.now() - startTime;
      if (elapsed > this.frameTimeLimit) {
        console.warn(`DOM patch exceeded frame budget: ${elapsed.toFixed(2)}ms`);
      }
    }
  }

  // Apply a single patch using morphdom
  private applyPatch(operation: PatchOperation) {
    const { element, newHTML, blockId } = operation;

    if (!element.isConnected) {
      console.warn(`Element for block ${blockId} is not connected to DOM`);
      return;
    }

    // Create temporary container for new content
    const tempContainer = document.createElement('div');
    tempContainer.innerHTML = newHTML;

    // Use morphdom for efficient DOM diffing
    morphdom(element, tempContainer, {
      childrenOnly: true,
      onBeforeElUpdated: (fromEl, toEl) => {
        // Preserve focus state
        if (fromEl === document.activeElement) {
          return false;
        }

        // Preserve scroll position for scrollable elements
        if (fromEl.scrollTop > 0) {
          (toEl as HTMLElement).scrollTop = fromEl.scrollTop;
        }

        return true;
      },
      onElUpdated: (el) => {
        // Trigger any necessary re-measurements
        if (el.hasAttribute('data-block-id')) {
          this.triggerResize(el as HTMLElement);
        }
      },
      onNodeAdded: (node) => {
        // Handle newly added nodes
        if (node.nodeType === Node.ELEMENT_NODE) {
          this.initializeNewElement(node as HTMLElement);
        }
      },
      onNodeDiscarded: (node) => {
        // Cleanup discarded nodes
        if (node.nodeType === Node.ELEMENT_NODE) {
          this.cleanupElement(node as HTMLElement);
        }
      }
    });
  }

  // Initialize newly added elements
  private initializeNewElement(element: HTMLElement) {
    // Add intersection observer for lazy loading
    if (element.querySelector('img, iframe')) {
      this.setupLazyLoading(element);
    }

    // Setup syntax highlighting for code blocks
    if (element.querySelector('pre code')) {
      this.setupSyntaxHighlighting(element);
    }
  }

  // Cleanup removed elements
  private cleanupElement(element: HTMLElement) {
    // Remove event listeners, observers, etc.
    const observers = (element as any).__observers;
    if (observers) {
      observers.forEach((observer: any) => observer.disconnect());
    }
  }

  // Trigger resize event for height recalculation
  private triggerResize(element: HTMLElement) {
    // Dispatch custom resize event
    const event = new CustomEvent('blockResize', {
      detail: { blockId: element.getAttribute('data-block-id') }
    });
    element.dispatchEvent(event);
  }

  // Setup lazy loading for images and iframes
  private setupLazyLoading(container: HTMLElement) {
    const lazyElements = container.querySelectorAll('img[data-src], iframe[data-src]');
    
    if (lazyElements.length === 0) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const element = entry.target as HTMLElement;
            const src = element.getAttribute('data-src');
            if (src) {
              element.setAttribute('src', src);
              element.removeAttribute('data-src');
              observer.unobserve(element);
            }
          }
        });
      },
      {
        rootMargin: '100px' // Load images 100px before they enter viewport
      }
    );

    lazyElements.forEach(el => observer.observe(el));

    // Store observer for cleanup
    (container as any).__observers = (container as any).__observers || [];
    (container as any).__observers.push(observer);
  }

  // Setup syntax highlighting for code blocks
  private setupSyntaxHighlighting(container: HTMLElement) {
    const codeBlocks = container.querySelectorAll('pre code');
    
    codeBlocks.forEach(block => {
      // Apply syntax highlighting if not already applied
      if (!block.classList.contains('hljs')) {
        // This would integrate with your syntax highlighting library
        // For now, just add a class to indicate it needs highlighting
        block.classList.add('needs-highlighting');
      }
    });
  }

  // Clear all pending patches
  clearPendingPatches() {
    this.pendingPatches.clear();
    
    if (this.rafId !== null) {
      cancelAnimationFrame(this.rafId);
      this.rafId = null;
    }
  }

  // Get performance metrics
  getMetrics() {
    return {
      pendingPatches: this.pendingPatches.size,
      isPatching: this.isPatching,
      queuedBatches: this.patchQueue.length
    };
  }

  // Destroy the patcher
  destroy() {
    this.clearPendingPatches();
    this.isPatching = false;
  }
}

// Singleton instance
let domPatcher: DOMPatcher | null = null;

export function getDOMPatcher(): DOMPatcher {
  if (!domPatcher) {
    domPatcher = new DOMPatcher();
  }
  return domPatcher;
}

export function destroyDOMPatcher() {
  if (domPatcher) {
    domPatcher.destroy();
    domPatcher = null;
  }
}

export { DOMPatcher };
