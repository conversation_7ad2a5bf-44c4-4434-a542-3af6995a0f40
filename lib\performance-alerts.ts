// Performance monitoring and alerting system
// Implements long task detection and FPS monitoring as recommended

interface PerformanceAlert {
  type: 'long_task' | 'low_fps' | 'memory_leak' | 'worker_timeout';
  severity: 'warning' | 'error' | 'critical';
  message: string;
  timestamp: number;
  data?: any;
}

interface PerformanceThresholds {
  longTaskMs: number;
  minFPS: number;
  maxMemoryMB: number;
  workerTimeoutMs: number;
}

class PerformanceAlerter {
  private alerts: PerformanceAlert[] = [];
  private thresholds: PerformanceThresholds;
  private callbacks = new Set<(alert: PerformanceAlert) => void>();
  private isMonitoring = false;
  private observer: PerformanceObserver | null = null;
  private fpsMonitor: number | null = null;
  private memoryMonitor: number | null = null;

  constructor(thresholds: Partial<PerformanceThresholds> = {}) {
    this.thresholds = {
      longTaskMs: 50, // 50ms as recommended
      minFPS: 55, // 55 FPS as target
      maxMemoryMB: 500, // 500MB memory limit
      workerTimeoutMs: 10000, // 10s worker timeout
      ...thresholds
    };
  }

  // Start monitoring
  startMonitoring() {
    if (this.isMonitoring) return;
    this.isMonitoring = true;

    this.setupLongTaskMonitoring();
    this.setupFPSMonitoring();
    this.setupMemoryMonitoring();
  }

  // Stop monitoring
  stopMonitoring() {
    if (!this.isMonitoring) return;
    this.isMonitoring = false;

    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }

    if (this.fpsMonitor) {
      cancelAnimationFrame(this.fpsMonitor);
      this.fpsMonitor = null;
    }

    if (this.memoryMonitor) {
      clearInterval(this.memoryMonitor);
      this.memoryMonitor = null;
    }
  }

  // Setup long task monitoring
  private setupLongTaskMonitoring() {
    if (!('PerformanceObserver' in window)) {
      console.warn('PerformanceObserver not supported');
      return;
    }

    try {
      this.observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        
        entries.forEach((entry) => {
          if (entry.entryType === 'longtask') {
            const duration = entry.duration;
            
            if (duration > this.thresholds.longTaskMs) {
              this.createAlert({
                type: 'long_task',
                severity: duration > 100 ? 'error' : 'warning',
                message: `Long task detected: ${duration.toFixed(2)}ms`,
                data: { duration, entry }
              });
            }
          }
        });
      });

      this.observer.observe({ entryTypes: ['longtask'] });
    } catch (error) {
      console.error('Failed to setup long task monitoring:', error);
    }
  }

  // Setup FPS monitoring
  private setupFPSMonitoring() {
    let frameCount = 0;
    let lastTime = performance.now();

    const measureFPS = () => {
      frameCount++;
      const now = performance.now();
      const delta = now - lastTime;

      if (delta >= 1000) {
        const fps = Math.round((frameCount * 1000) / delta);
        frameCount = 0;
        lastTime = now;

        if (fps < this.thresholds.minFPS) {
          this.createAlert({
            type: 'low_fps',
            severity: fps < 30 ? 'error' : 'warning',
            message: `Low FPS detected: ${fps}`,
            data: { fps, target: this.thresholds.minFPS }
          });
        }
      }

      if (this.isMonitoring) {
        this.fpsMonitor = requestAnimationFrame(measureFPS);
      }
    };

    this.fpsMonitor = requestAnimationFrame(measureFPS);
  }

  // Setup memory monitoring
  private setupMemoryMonitoring() {
    if (!('memory' in performance)) {
      console.warn('Memory monitoring not supported');
      return;
    }

    this.memoryMonitor = setInterval(() => {
      const memory = (performance as any).memory;
      const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);

      if (usedMB > this.thresholds.maxMemoryMB) {
        this.createAlert({
          type: 'memory_leak',
          severity: usedMB > this.thresholds.maxMemoryMB * 1.5 ? 'critical' : 'warning',
          message: `High memory usage: ${usedMB}MB`,
          data: { 
            usedMB, 
            limitMB: this.thresholds.maxMemoryMB,
            totalMB: Math.round(memory.totalJSHeapSize / 1024 / 1024)
          }
        });
      }
    }, 5000); // Check every 5 seconds
  }

  // Create and dispatch alert
  private createAlert(alertData: Omit<PerformanceAlert, 'timestamp'>) {
    const alert: PerformanceAlert = {
      ...alertData,
      timestamp: Date.now()
    };

    this.alerts.push(alert);

    // Keep only last 100 alerts
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(-100);
    }

    // Notify callbacks
    this.callbacks.forEach(callback => {
      try {
        callback(alert);
      } catch (error) {
        console.error('Error in performance alert callback:', error);
      }
    });

    // Log to console based on severity
    const logMethod = alert.severity === 'critical' ? 'error' : 
                     alert.severity === 'error' ? 'error' : 'warn';
    console[logMethod](`[Performance Alert] ${alert.message}`, alert.data);
  }

  // Add alert callback
  onAlert(callback: (alert: PerformanceAlert) => void) {
    this.callbacks.add(callback);
    return () => this.callbacks.delete(callback);
  }

  // Get recent alerts
  getAlerts(count = 10): PerformanceAlert[] {
    return this.alerts.slice(-count);
  }

  // Get alerts by type
  getAlertsByType(type: PerformanceAlert['type']): PerformanceAlert[] {
    return this.alerts.filter(alert => alert.type === type);
  }

  // Clear alerts
  clearAlerts() {
    this.alerts = [];
  }

  // Update thresholds
  updateThresholds(newThresholds: Partial<PerformanceThresholds>) {
    this.thresholds = { ...this.thresholds, ...newThresholds };
  }

  // Get current thresholds
  getThresholds(): PerformanceThresholds {
    return { ...this.thresholds };
  }

  // Manual alert creation (for custom monitoring)
  createManualAlert(
    type: PerformanceAlert['type'],
    message: string,
    severity: PerformanceAlert['severity'] = 'warning',
    data?: any
  ) {
    this.createAlert({ type, message, severity, data });
  }

  // Get performance summary
  getPerformanceSummary() {
    const now = Date.now();
    const recentAlerts = this.alerts.filter(alert => now - alert.timestamp < 60000); // Last minute

    return {
      totalAlerts: this.alerts.length,
      recentAlerts: recentAlerts.length,
      alertsByType: {
        long_task: this.getAlertsByType('long_task').length,
        low_fps: this.getAlertsByType('low_fps').length,
        memory_leak: this.getAlertsByType('memory_leak').length,
        worker_timeout: this.getAlertsByType('worker_timeout').length
      },
      isMonitoring: this.isMonitoring,
      thresholds: this.thresholds
    };
  }

  // Destroy alerter
  destroy() {
    this.stopMonitoring();
    this.callbacks.clear();
    this.alerts = [];
  }
}

// Singleton instance
let globalAlerter: PerformanceAlerter | null = null;

export function getPerformanceAlerter(): PerformanceAlerter {
  if (!globalAlerter) {
    globalAlerter = new PerformanceAlerter();
  }
  return globalAlerter;
}

export function destroyPerformanceAlerter() {
  if (globalAlerter) {
    globalAlerter.destroy();
    globalAlerter = null;
  }
}

// React hook for performance alerts
export function usePerformanceAlerts() {
  const alerter = getPerformanceAlerter();
  
  return {
    startMonitoring: () => alerter.startMonitoring(),
    stopMonitoring: () => alerter.stopMonitoring(),
    onAlert: (callback: (alert: PerformanceAlert) => void) => alerter.onAlert(callback),
    getAlerts: (count?: number) => alerter.getAlerts(count),
    clearAlerts: () => alerter.clearAlerts(),
    getSummary: () => alerter.getPerformanceSummary(),
    createAlert: (type: PerformanceAlert['type'], message: string, severity?: PerformanceAlert['severity'], data?: any) => 
      alerter.createManualAlert(type, message, severity, data)
  };
}

export type { PerformanceAlert, PerformanceThresholds };
export { PerformanceAlerter };
